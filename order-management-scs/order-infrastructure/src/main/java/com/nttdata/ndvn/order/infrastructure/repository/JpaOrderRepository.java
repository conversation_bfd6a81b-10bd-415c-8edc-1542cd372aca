package com.nttdata.ndvn.order.infrastructure.repository;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderStatus;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA implementation of OrderRepository.
 * 
 * This repository provides data access operations for Order entities
 * using Spring Data JPA.
 */
@Repository
public interface JpaOrderRepository extends JpaRepository<Order, UUID>, OrderRepository {
    
    @Override
    Optional<Order> findByOrderNumber(String orderNumber);
    
    @Override
    Page<Order> findByCustomerId(UUID customerId, Pageable pageable);
    
    @Override
    Page<Order> findByUserId(UUID userId, Pageable pageable);
    
    @Override
    Page<Order> findByStatus(OrderStatus status, Pageable pageable);
    
    @Override
    Page<Order> findByStatusIn(List<OrderStatus> statuses, Pageable pageable);
    
    @Override
    Page<Order> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    @Override
    Page<Order> findByCustomerIdAndStatus(UUID customerId, OrderStatus status, Pageable pageable);
    
    @Override
    Page<Order> findByCustomerIdAndCreatedAtBetween(UUID customerId, LocalDateTime startDate, 
                                                   LocalDateTime endDate, Pageable pageable);
    
    @Override
    @Query("SELECT o FROM Order o WHERE o.status = 'PAID'")
    Page<Order> findOrdersReadyForProcessing(Pageable pageable);
    
    @Override
    @Query("SELECT o FROM Order o WHERE o.status IN ('PAID', 'PROCESSING')")
    Page<Order> findOrdersReadyForFulfillment(Pageable pageable);
    
    @Override
    @Query("SELECT o FROM Order o WHERE o.status = 'FULFILLED'")
    Page<Order> findOrdersReadyForShipping(Pageable pageable);
    
    @Override
    @Query("SELECT o FROM Order o WHERE o.status = 'DRAFT' AND o.createdAt < :cutoffTime")
    default List<Order> findAbandonedOrders(int hoursAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(hoursAgo);
        return findAbandonedOrdersByCutoffTime(cutoffTime);
    }

    @Query("SELECT o FROM Order o WHERE o.status = 'DRAFT' AND o.createdAt < :cutoffTime")
    List<Order> findAbandonedOrdersByCutoffTime(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    @Override
    long countByStatus(OrderStatus status);
    
    @Override
    long countByCustomerId(UUID customerId);
    
    @Override
    boolean existsByOrderNumber(String orderNumber);
    
    // Additional JPA-specific queries
    
    @Query("SELECT o FROM Order o WHERE o.customerId = :customerId AND o.status NOT IN ('CANCELLED', 'REFUNDED') ORDER BY o.createdAt DESC")
    List<Order> findActiveOrdersByCustomerId(@Param("customerId") UUID customerId);
    
    @Query("SELECT o FROM Order o WHERE o.totalAmount > :amount AND o.status = :status")
    Page<Order> findOrdersByAmountGreaterThanAndStatus(@Param("amount") java.math.BigDecimal amount, 
                                                      @Param("status") OrderStatus status, 
                                                      Pageable pageable);
    
    @Query("SELECT COUNT(o) FROM Order o WHERE o.customerId = :customerId AND o.createdAt >= :startDate")
    long countOrdersByCustomerSince(@Param("customerId") UUID customerId, @Param("startDate") LocalDateTime startDate);
    
    @Query("SELECT o FROM Order o JOIN FETCH o.items WHERE o.id = :orderId")
    Optional<Order> findByIdWithItems(@Param("orderId") UUID orderId);
    
    @Query("SELECT o FROM Order o JOIN FETCH o.payments WHERE o.id = :orderId")
    Optional<Order> findByIdWithPayments(@Param("orderId") UUID orderId);
    
    @Query("SELECT o FROM Order o JOIN FETCH o.shipments WHERE o.id = :orderId")
    Optional<Order> findByIdWithShipments(@Param("orderId") UUID orderId);
    
    @Query("SELECT o FROM Order o " +
           "LEFT JOIN FETCH o.items " +
           "LEFT JOIN FETCH o.payments " +
           "LEFT JOIN FETCH o.shipments " +
           "WHERE o.id = :orderId")
    Optional<Order> findByIdWithAllRelations(@Param("orderId") UUID orderId);
    
    @Query("SELECT DISTINCT o FROM Order o " +
           "JOIN o.items i " +
           "WHERE i.productId = :productId")
    Page<Order> findOrdersByProductId(@Param("productId") UUID productId, Pageable pageable);
    
    @Query("SELECT o FROM Order o WHERE o.status = :status AND o.updatedAt < :cutoffTime")
    List<Order> findStaleOrdersByStatus(@Param("status") OrderStatus status, 
                                       @Param("cutoffTime") LocalDateTime cutoffTime);
    
    @Query("SELECT o.status, COUNT(o) FROM Order o GROUP BY o.status")
    List<Object[]> getOrderCountByStatus();
    
    @Query("SELECT DATE(o.createdAt), COUNT(o) FROM Order o " +
           "WHERE o.createdAt >= :startDate " +
           "GROUP BY DATE(o.createdAt) " +
           "ORDER BY DATE(o.createdAt)")
    List<Object[]> getOrderCountByDate(@Param("startDate") LocalDateTime startDate);
    
    // Implementation of custom methods from OrderRepository interface
}
